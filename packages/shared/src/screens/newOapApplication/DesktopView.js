import React, { useState, useEffect, useRef } from 'react'
import {
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  useWindowDimensions,
  Platform,
  Animated,
} from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { isTablet, isWeb } from '@libs/utils/src/screenLayout'
import { spacing, useTheme } from '@libs/theme'
import { Text, ShimmerPlaceholder } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { OapPortal } from '../../components'
import { findPaddingHorizontal } from '../../utils/findPaddingHorizontal'
import { TitleHeader } from '../../components/headerTitle'
import { getOAPUrl } from '../../utils/getOAPUrl'

// Creative Loading Component
export const ApplicationLoadingState = () => {
  const { colors } = useTheme()
  const pulseAnim = useRef(new Animated.Value(0.3)).current
  const slideAnim = useRef(new Animated.Value(-50)).current
  const rotateAnim = useRef(new Animated.Value(0)).current
  const scaleAnim = useRef(new Animated.Value(0.8)).current

  useEffect(() => {
    // Pulse animation for the main icon
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.3,
          duration: 1200,
          useNativeDriver: true,
        }),
      ]),
    )

    // Slide animation for progress dots
    const slideAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -50,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]),
    )

    // Rotation animation for the outer ring
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      }),
    )

    // Scale animation for the container
    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.05,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    )

    pulseAnimation.start()
    slideAnimation.start()
    rotateAnimation.start()
    scaleAnimation.start()

    return () => {
      pulseAnimation.stop()
      slideAnimation.stop()
      rotateAnimation.stop()
      scaleAnimation.stop()
    }
  }, [])

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  })

  return (
    <View style={loadingStyles.container}>
      <Animated.View
        style={[
          loadingStyles.mainContainer,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Outer rotating ring */}
        <Animated.View
          style={[
            loadingStyles.outerRing,
            {
              borderColor: colors.primary,
              transform: [{ rotate: spin }],
            },
          ]}
        />

        {/* Main pulsing icon */}
        <Animated.View
          style={[
            loadingStyles.iconContainer,
            {
              opacity: pulseAnim,
              backgroundColor: colors.primaryContainer,
            },
          ]}
        >
          <Icon
            name="AuthLoading"
            style={[loadingStyles.icon, { tintColor: colors.primary }]}
          />
        </Animated.View>

        {/* Progress dots */}
        <View style={loadingStyles.dotsContainer}>
          {[0, 1, 2].map((index) => (
            <Animated.View
              key={index}
              style={[
                loadingStyles.dot,
                {
                  backgroundColor: colors.primary,
                  transform: [
                    {
                      translateX: Animated.add(
                        slideAnim,
                        new Animated.Value(index * 15),
                      ),
                    },
                  ],
                  opacity: pulseAnim,
                },
              ]}
            />
          ))}
        </View>
      </Animated.View>

      {/* Loading text with shimmer effect */}
      <View style={loadingStyles.textContainer}>
        <Text
          variant="heading3"
          style={[loadingStyles.loadingText, { color: colors.primary }]}
        >
          Preparing Your Application Portal
        </Text>
        <ShimmerPlaceholder
          style={[
            loadingStyles.shimmerBar,
            { backgroundColor: colors.primaryContainer },
          ]}
          shimmerColors={[
            colors.primaryContainer,
            colors.primary,
            colors.primaryContainer,
          ]}
        />
        <Text
          variant="body2"
          style={[loadingStyles.subText, { color: colors.onSurface }]}
        >
          Setting up secure connection...
        </Text>
      </View>
    </View>
  )
}

const loadingStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  mainContainer: {
    position: 'relative',
    width: 160,
    height: 160,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  outerRing: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 3,
    borderStyle: 'dashed',
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  icon: {
    width: 60,
    height: 60,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: -30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: '600',
  },
  shimmerBar: {
    width: 200,
    height: 4,
    borderRadius: 2,
    marginBottom: 12,
  },
  subText: {
    textAlign: 'center',
    opacity: 0.7,
  },
})

const DesktopView = ({
  opportunities,
  isOpportunitiesFetching,
  scrollViewRef,
  tokens,
  isTokensFetching,
  updateOpportunity: updateOpportunityAfterSubmission,
}) => {
  const { colors } = useTheme()
  const { width } = useWindowDimensions()
  const web = isWeb(width)
  const tablet = isTablet(width)
  const navigation = useNavigation()
  const [submissionConfirmed, setSubmissionConfirmed] = useState(false)
  const [countdown, setCountdown] = useState(30)

  // Auto-redirect countdown when submission is confirmed
  useEffect(() => {
    let interval = null
    if (submissionConfirmed) {
      interval = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            // Redirect to my-application screen
            if (Platform.OS === 'web') {
              window.history.pushState({}, '', '/my-application')
              window.location.reload()
            } else {
              navigation.navigate('my-application')
            }
            return 0
          }
          return prevCountdown - 1
        })
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [submissionConfirmed, navigation])

  if (isOpportunitiesFetching) {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          flexDirection: 'row',
          paddingHorizontal: findPaddingHorizontal(width),
          minHeight: '100vh',
        }}
      >
        <View
          style={{
            flexDirection: 'column',
            flex: 1,
            marginTop: 56,
          }}
        >
          {web && (
            <TouchableOpacity
              onPress={() => {
                if (Platform.OS === 'web') {
                  window.history.go(-1)
                } else {
                  // navigation.goBack()
                }
              }}
              style={{
                marginBottom: !tablet && spacing.spacing2,
                flexDirection: 'row',
                columnGap: 14,
              }}
            >
              <Icon name="ArrowNarrowLeft" height={20} width={20} />
              <Text
                style={{
                  textTransform: 'uppercase',
                  colors: colors.onNeutral,
                  letterSpacing: '1.5%',
                }}
              >
                Back to applications
              </Text>
            </TouchableOpacity>
          )}
          <ShimmerPlaceholder
            style={{
              height: 150,
              width: '100%',
              marginTop: spacing.spacing5,
              borderRadius: spacing.spacing5,
            }}
          />

          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: '100%',
              gap: 24,
            }}
          >
            <ShimmerPlaceholder
              style={{
                height: 450,
                marginTop: spacing.spacing5,
                borderRadius: spacing.spacing5,
                flex: 1,
              }}
            />
          </View>
        </View>
      </View>
    )
  }

  const programName = opportunities?.ProgrammeName__c
    ? `${opportunities?.ProgrammeName__c} ${
        opportunities?.Delivery_Mode__c
          ? `, ${opportunities?.Delivery_Mode__c}`
          : ''
      }`
    : ''

  return (
    <ScrollView
      style={{
        backgroundColor: colors.backgroundPrimary,
        paddingHorizontal: findPaddingHorizontal(width),
      }}
      contentContainerStyle={{
        marginVertical: 57,
        flexGrow: 1,
      }}
      ref={scrollViewRef}
    >
      {web && (
        <TouchableOpacity
          onPress={() => {
            if (Platform.OS === 'web') {
              window.history.go(-1)
            } else {
              // navigation.goBack()
            }
          }}
          style={{
            marginBottom: !tablet && spacing.spacing6,
            flexDirection: 'row',
            columnGap: 14,
          }}
        >
          <Icon name="ArrowNarrowLeft" height={20} width={20} />
          <Text
            style={{
              textTransform: 'uppercase',
              colors: colors.onNeutral,
              letterSpacing: '1.5%',
            }}
          >
            Back to applications
          </Text>
        </TouchableOpacity>
      )}

      <TitleHeader>
        <View style={{ flexDirection: 'row' }}>
          <Text
            variant="heading3"
            color={colors.white}
            style={{
              alignSelf: 'center',
            }}
          >
            {programName || opportunities?.Name?.split('_')[0]}
          </Text>
          <Text
            variant="heading3"
            color={colors.white}
            style={{
              alignSelf: 'center',
            }}
          >
            - {opportunities?.Institution}
          </Text>
        </View>
      </TitleHeader>
      {isTokensFetching ? (
        <ApplicationLoadingState />
      ) : (
        <View style={styles.container}>
          <OapPortal
            portalUrl={getOAPUrl(opportunities)}
            redirectPath="/application-filter"
            opportunityDetails={opportunities}
            userType={
              opportunities?.ApplicationSource__c?.toLowerCase() === 'oap'
                ? 'student'
                : 'agent'
            }
            applicationStatus={
              opportunities?.ApplicationStatus__c ? 'new' : 'draft'
            }
            userEmail={opportunities?.Account?.PersonEmail || ''}
            tokens={tokens}
            handleSubmissionConfirmation={(data) => {
              if (data) {
                setSubmissionConfirmed(true)
                updateOpportunityAfterSubmission()
                setCountdown(30) // Reset countdown when submission is confirmed
                // Scroll to bottom to show the confirmation message
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true })
                }, 100)
              }
            }}
          />
          {submissionConfirmed && (
            <View style={styles.confirmationMessage}>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ textAlign: 'center', marginBottom: 8 }}
              >
                You will redirected to My application page in{' '}
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: '600',
                    textDecorationLine: 'underline',
                  }}
                >
                  {countdown}
                </Text>
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: '600',
                    paddingLeft: 4,
                  }}
                  variant="display4"
                >
                  sec
                </Text>
              </Text>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ textAlign: 'center', marginBottom: 8 }}
              >
                or
              </Text>
              <TouchableOpacity
                onPress={() => {
                  if (Platform.OS === 'web') {
                    window.history.pushState({}, '', '/my-application')
                    window.location.reload()
                  } else {
                    navigation.navigate('my-application')
                  }
                }}
                style={styles.redirectLink}
              >
                Click here to go to
                <Text
                  variant="display4"
                  color={colors.primary}
                  style={{
                    textAlign: 'center',
                    textDecorationLine: 'underline',
                    fontWeight: '600',
                  }}
                >
                  My application page →
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  programHeader: {
    backgroundColor: '#2C3E50',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 280,
    backgroundColor: '#4A90E2',
    paddingVertical: 24,
  },
  brandContainer: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  brandLogo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  stepsList: {
    paddingHorizontal: 24,
    flex: 1,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumber: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    flex: 1,
    fontSize: 12,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  formContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  formScrollView: {
    flex: 1,
    paddingHorizontal: 40,
    paddingVertical: 32,
  },
  formTitle: {
    marginBottom: 32,
    color: '#2C3E50',
  },
  fieldContainer: {
    marginBottom: 24,
    position: 'relative',
  },
  fieldLabel: {
    marginBottom: 8,
    color: '#2C3E50',
    fontSize: 14,
  },
  required: {
    color: '#D72C2C',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  fieldIcon: {
    position: 'absolute',
    right: 12,
    top: 36,
  },
  buttonContainer: {
    marginTop: 32,
    alignItems: 'flex-start',
  },
  nextButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 12,
  },
  confirmationMessage: {
    marginTop: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  redirectLink: {
    paddingHorizontal: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    fontWeight: '600',
    fontSize: 14,
  },
})

export default DesktopView
